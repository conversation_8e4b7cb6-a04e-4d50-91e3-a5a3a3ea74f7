﻿@page "/"
@using Blazorise.Charts
@inject IGenericRepository<DashboardStatsModel> dashboardRepo
@inject IGenericRepository<ProgramModel> programRepo
@inject IGenericRepository<ActivityModel> activityRepo
@inject IGenericRepository<StudentPaymentModel> paymentRepo
@inject IGenericRepository<EnrollmentDetailsModel> enrollmentRepo
@attribute [Authorize]

<div class="dashboard-container fade-in">

    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading dashboard data...</p>
        </div>
    }
    else
    {
        <!-- Stats Cards Row -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-primary shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Active Programs</h5>
                                <p class="card-text display-6 fw-bold">@dashboardData.ActiveProgramsCount</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-book fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-success shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Total Staff</h5>
                                <p class="card-text display-6 fw-bold">@dashboardData.StaffCount</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-people fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-info shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Enrolled Students</h5>
                                <p class="card-text display-6 fw-bold">@dashboardData.EnrolledStudentsInActivePrograms</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-person-check fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-warning shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">This Month Revenue</h5>
                                <p class="card-text display-6 fw-bold">@currentMonthRevenue.ToString("N0")</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-currency-dollar fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Monthly Enrollments Chart -->
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            Monthly Enrollments (Last 6 Months)
                        </h5>
                    </div>
                    <div class="card-body">
                        <LineChart @ref="enrollmentChart" TItem="double" />
                    </div>
                </div>
            </div>

            <!-- Monthly Revenue Chart -->
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card shadow">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-cash-stack me-2"></i>
                            Monthly Revenue (Last 6 Months)
                        </h5>
                    </div>
                    <div class="card-body">
                        <BarChart @ref="revenueChart" TItem="double" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Program Deadlines -->
        <div class="card dashboard-card border-0 shadow mb-5">
            <div class="card-body">
                <h4 class="card-title mb-4">
                    <i class="bi bi-calendar-event me-2"></i>
                    Upcoming Program Deadlines
                </h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="border rounded p-3 bg-light">
                            <h5 class="text-primary">
                                <i class="bi bi-play-circle me-2"></i>
                                Starting in Next 2 Weeks
                            </h5>
                            @if (startingSoon.Any())
                            {
                                <ul class="list-group list-group-flush">
                                    @foreach (var p in startingSoon)
                                    {
                                        <li class="list-group-item dashboard-deadline-item d-flex justify-content-between align-items-center">
                                            <span>@p.Name</span>
                                            <span class="badge dashboard-badge bg-primary rounded-pill">@p.StartingDate.ToString("MMM dd")</span>
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted fst-italic">None</p>
                            }
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 bg-light">
                            <h5 class="text-danger">
                                <i class="bi bi-stop-circle me-2"></i>
                                Ending in Next 2 Weeks
                            </h5>
                            @if (endingSoon.Any())
                            {
                                <ul class="list-group list-group-flush">
                                    @foreach (var p in endingSoon)
                                    {
                                        <li class="list-group-item dashboard-deadline-item d-flex justify-content-between align-items-center">
                                            <span>@p.Name</span>
                                            <span class="badge dashboard-badge bg-danger rounded-pill">@p.EndingDate.ToString("MMM dd")</span>
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted fst-italic">None</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-md-6">
                <div class="card dashboard-card shadow mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-credit-card me-2"></i>
                            Recent Payments
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Student</th>
                                        <th>Program</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var payment in recentPayments.Take(10))
                                    {
                                        <tr class="dashboard-table-row">
                                            <td>@PersianDateConverter.ToPersianString(payment.PaymentDate)</td>
                                            <td>@payment.StudentName</td>
                                            <td>@payment.ProgramName</td>
                                            <td class="fw-bold text-success">@payment.PaidAmount.ToString("N0")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card shadow mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-person-plus me-2"></i>
                            Recent Enrollments
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Student</th>
                                        <th>Program</th>
                                        <th>Section</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var enrollment in recentEnrollments.Take(10))
                                    {
                                        <tr class="dashboard-table-row">
                                            <td>@PersianDateConverter.ToPersianString(enrollment.EnrollmentDate)</td>
                                            <td>@enrollment.StudentName @enrollment.StudentLastName</td>
                                            <td>@enrollment.ProgramName</td>
                                            <td>@enrollment.SectionName</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private DashboardStatsModel dashboardData = new();
    private List<ProgramModel> programs = new();
    private List<ProgramModel> startingSoon = new();
    private List<ProgramModel> endingSoon = new();
    private List<ActivityModel> recentActivities = new();
    private List<StudentPaymentModel> recentPayments = new();
    private List<EnrollmentDetailsModel> recentEnrollments = new();
    private decimal currentMonthRevenue = 0;
    private bool isLoading = true;

    // Chart references
    private LineChart<double> enrollmentChart = new();
    private BarChart<double> revenueChart = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
        await LoadChartData();
        isLoading = false;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await SetupCharts();
        }
    }

    private async Task LoadDashboardData()
    {
        // Load dashboard stats using your existing stored procedure
        dashboardData = await dashboardRepo.GetByIdAsync("DashboardsProcedure", null);

        // Load programs using your existing stored procedure
        programs = (await programRepo.GetAllAsync("GetAllPrograms")).ToList();

        var today = DateTime.Today;
        var cutoff = today.AddDays(14);

        startingSoon = programs
            .Where(p => p.IsActive == 1 && p.StartingDate.Date >= today && p.StartingDate.Date <= cutoff)
            .OrderBy(p => p.StartingDate)
            .ToList();

        endingSoon = programs
            .Where(p => p.IsActive == 1 && p.EndingDate.Date >= today && p.EndingDate.Date <= cutoff)
            .OrderBy(p => p.EndingDate)
            .ToList();

        // Load recent activities using your existing stored procedure
        recentActivities = (await activityRepo.GetAllAsync("GetRecentActivities")).ToList();

        // Load recent payments using your existing stored procedure
        var allPayments = (await paymentRepo.GetAllAsync("GetAllStudentPayments")).ToList();
        recentPayments = allPayments
            .OrderByDescending(p => p.PaymentDate)
            .Take(10)
            .ToList();

        // Load recent enrollments using your existing stored procedure
        var allEnrollments = (await enrollmentRepo.GetAllAsync("GetAllEnrollmentDetails")).ToList();
        recentEnrollments = allEnrollments
            .OrderByDescending(e => e.EnrollmentDate)
            .Take(10)
            .ToList();
    }

    private async Task LoadChartData()
    {
        // Calculate current month revenue from existing payment data
        var currentMonth = DateTime.Now.Month;
        var currentYear = DateTime.Now.Year;

        currentMonthRevenue = recentPayments
            .Where(p => p.PaymentDate.Month == currentMonth && p.PaymentDate.Year == currentYear)
            .Sum(p => p.PaidAmount);
    }

    private async Task SetupCharts()
    {
        // Setup Enrollment Chart using existing data
        await enrollmentChart.Clear();
        await enrollmentChart.AddLabelsDatasetsAndUpdate(
            GetMonthLabels(),
            GetEnrollmentDataset()
        );

        // Setup Revenue Chart using existing data
        await revenueChart.Clear();
        await revenueChart.AddLabelsDatasetsAndUpdate(
            GetMonthLabels(),
            GetRevenueDataset()
        );
    }

    private List<string> GetMonthLabels()
    {
        var labels = new List<string>();
        var currentDate = DateTime.Now.AddMonths(-5);

        for (int i = 0; i < 6; i++)
        {
            labels.Add(currentDate.ToString("MMM yyyy"));
            currentDate = currentDate.AddMonths(1);
        }

        return labels;
    }

    private LineChartDataset<double> GetEnrollmentDataset()
    {
        var data = new List<double>();
        var currentDate = DateTime.Now.AddMonths(-5);

        for (int i = 0; i < 6; i++)
        {
            var monthEnrollments = recentEnrollments
                .Where(e => e.EnrollmentDate.Month == currentDate.Month && e.EnrollmentDate.Year == currentDate.Year)
                .Count();
            data.Add(monthEnrollments);
            currentDate = currentDate.AddMonths(1);
        }

        return new LineChartDataset<double>
            {
                Label = "Enrollments",
                Data = data,
                BackgroundColor = new List<string> { "rgba(54, 162, 235, 0.2)" },
                BorderColor = new List<string> { "rgba(54, 162, 235, 1)" },
                BorderWidth = 3,
                Fill = true
               
            };
    }

    private BarChartDataset<double> GetRevenueDataset()
    {
        var data = new List<double>();
        var currentDate = DateTime.Now.AddMonths(-5);

        for (int i = 0; i < 6; i++)
        {
            var monthRevenue = recentPayments
                .Where(p => p.PaymentDate.Month == currentDate.Month && p.PaymentDate.Year == currentDate.Year)
                .Sum(p => (double)p.PaidAmount);
            data.Add(monthRevenue);
            currentDate = currentDate.AddMonths(1);
        }

        return new BarChartDataset<double>
            {
                Label = "Revenue (AFN)",
                Data = data,
                BackgroundColor = new List<string>
            {
                "rgba(75, 192, 192, 0.6)",
                "rgba(255, 99, 132, 0.6)",
                "rgba(255, 205, 86, 0.6)",
                "rgba(54, 162, 235, 0.6)",
                "rgba(153, 102, 255, 0.6)",
                "rgba(255, 159, 64, 0.6)"
            },
                BorderColor = new List<string>
            {
                "rgba(75, 192, 192, 1)",
                "rgba(255, 99, 132, 1)",
                "rgba(255, 205, 86, 1)",
                "rgba(54, 162, 235, 1)",
                "rgba(153, 102, 255, 1)",
                "rgba(255, 159, 64, 1)"
            },
                BorderWidth = 2
            };
    }

    // Your existing models
    public class DashboardStatsModel
    {
        public int ActiveProgramsCount { get; set; }
        public int StaffCount { get; set; }
        public int EnrolledStudentsInActivePrograms { get; set; }
    }

    public class ProgramModel
    {
        public int ProgramId { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime StartingDate { get; set; }
        public DateTime EndingDate { get; set; }
        public int IsActive { get; set; }
        public decimal Fee { get; set; }
    }

    public class ActivityModel
    {
        public string ActivityType { get; set; } = string.Empty;
        public DateTime ActivityDate { get; set; }
        public string Student { get; set; } = string.Empty;
        public string Program { get; set; } = string.Empty;
        public string ShiftName { get; set; } = string.Empty;
        public decimal AmountPaid { get; set; }
    }

    public class StudentPaymentModel
    {
        public int PaymentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string ProgramName { get; set; } = string.Empty;
        public decimal PaidAmount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string BillNo { get; set; } = string.Empty;
        public string FatherName { get; set; } = string.Empty;
        public string StudentIdNumber { get; set; } = string.Empty;
        public decimal ProgramFee { get; set; }
        public decimal RemainingAmount { get; set; }
        public int Period { get; set; }
        public int StudentId { get; set; }
    }

    public class EnrollmentDetailsModel
    {
        public int EnrollmentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentLastName { get; set; } = string.Empty;
        public string StudentFatherName { get; set; } = string.Empty;
        public int ProgramId { get; set; }
        public string ProgramName { get; set; } = string.Empty;
        public DateTime ProgramStartingDate { get; set; }
        public DateTime ProgramEndingDate { get; set; }
        public string CalculatedDuration { get; set; } = string.Empty;
        public int SectionId { get; set; }
        public string SectionName { get; set; } = string.Empty;
        public int ShiftId { get; set; }
        public string ShiftName { get; set; } = string.Empty;
        public DateTime EnrollmentDate { get; set; }
    }
}