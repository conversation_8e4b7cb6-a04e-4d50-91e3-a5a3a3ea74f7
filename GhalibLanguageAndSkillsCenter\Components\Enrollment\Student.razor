﻿@page "/student"
@inject IGenericRepository<StudentModel> studentRepository

@rendermode InteractiveServer
@using System.ComponentModel.DataAnnotations;
@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject NavigationManager NavigationManager
@inject IWebHostEnvironment Env
@inject MessageServices MessageServices
@attribute [Authorize]
@if (!string.IsNullOrEmpty(message))
{
    <div class="@messageClass" role="alert">@message</div>
}
@if (!string.IsNullOrEmpty(MessageServices.Message))
{
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> @MessageServices.Message
    </div>
}



@if (isGivingDiscount)
{
    <h3>Give Discount</h3>
    <label>Discount for Student ID: @discount.StudentId</label>

    <div class="form-group">
        <label>Select Program</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram SelectedProgramId="@discount.ProgramId"
        SelectedProgramIdChanged="@((int id) => discount.ProgramId = id)" />
    </div>

    <div class="form-group">
        <label>Discount Percentage</label>
        <InputNumber @bind-Value="discount.DiscountPercentage" class="form-control" />
    </div>

    <div class="form-group">
        <label>Reason (optional)</label>
        <InputText @bind-Value="discount.Reason" class="form-control" />
    </div>

    <button class="btn btn-primary mt-2" @onclick="SubmitDiscount">Apply Discount</button>
    <button class="btn btn-secondary mt-2 ms-2" @onclick="CancelDiscount">Cancel</button>
}


<hr />
<h3>Student List</h3>

<!-- Search box -->
<div class="form-group mb-3">
    <label for="searchInput">Search Students</label>
    <input type="text" id="searchInput" class="form-control"
    @bind="searchTerm" @bind:event="oninput"
    placeholder="Search by name, last name, or contact" />
</div>

@if (FilteredStudents is not null && FilteredStudents.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>

                <th>Name</th>
                <th>Last Name</th>
                <th>Father Name</th>
                <th>Contact</th>
                <th>Age</th>
                <th>TazkeraNumber</th>
                <th>Address</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var student in FilteredStudents)
            {
                <tr>

                    <td>@student.Name</td>
                    <td>@student.LastName</td>
                    <td>@student.FatherName</td>
                    <td>@student.Contact</td>
                    <td>@student.Age</td>
                    <td>@student.TazkeraNumber</td>
                    <td>@student.Address</td>
                    <td>
                        <button class="btn btn-primary btn-sm rounded-pill px-3 shadow-sm" @onclick="() => GotoDetails(student.StudentId)">
                            <i class="fas fa-pen-to-square me-1"></i> Edit
                        </button>


                      
                        <button class="btn btn-success btn-sm ms-2" @onclick="() => StartGivingDiscount(student.StudentId)">Discount</button>


                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No students found.</p>
}

@code {
    private StudentModel newStudent = new();
    private List<StudentModel> students = new();
   
 
    private string? message;
    private string? messageClass;
    private bool isGivingDiscount = false;
    private DiscountModel discount = new();


    private string searchTerm = string.Empty;

    private IEnumerable<StudentModel> FilteredStudents =>
        string.IsNullOrWhiteSpace(searchTerm)
            ? students
            : students.Where(s =>
                (s.Name?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (s.LastName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (s.Contact?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false));

    protected override async Task OnInitializedAsync()
    {
        await LoadStudents();
    }
    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file == null)
            return;

        // 1) Compute a unique filename and the folder under wwwroot
        var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name)}";
        var imagesFolder = Path.Combine(Env.WebRootPath, "images");
        Directory.CreateDirectory(imagesFolder); // creates if missing

        // 2) Full disk path to wwwroot/images/<fileName>
        var absolutePath = Path.Combine(imagesFolder, fileName);

        // 3) Copy the uploaded file stream into that path
        await using (var fileStream = new FileStream(absolutePath, FileMode.Create))
        {
            // restrict max size as you see fit (e.g. 5 MB)
            await file.OpenReadStream(5 * 1024 * 1024).CopyToAsync(fileStream);
        }

        // 4) Store the *relative* URL (to use in the DB and for <img src=...> later)
        newStudent.ProfileImage = $"/images/{fileName}";
    }

    private async Task LoadStudents()
    {
        students = (await studentRepository.GetAllAsync("GetAllStudentDetails")).ToList();

    }
    private void GotoDetails(int studentid)
    {
        NavigationManager.NavigateTo("/StudentDetails/" + studentid);
    }
   


   


    

 

    private void StartGivingDiscount(int studentId)
    {
        isGivingDiscount = true;
       

        discount = new DiscountModel
            {
                StudentId = studentId
            };
    }

    private async Task SubmitDiscount()
    {
        if (discount.ProgramId == 0)
        {
            message = "Please select a program.";
            messageClass = "alert alert-warning";
            return;
        }

        await studentRepository.AddAsync("AddStudentDiscount", new
        {
            discount.DiscountPercentage,
            discount.Reason,
            discount.StudentId,
            discount.ProgramId
        });

        isGivingDiscount = false;
        message = "Discount applied successfully.";
        messageClass = "alert alert-success";
    }

    private void CancelDiscount()
    {
        isGivingDiscount = false;
        discount = new();
    }





    
    [Inject]
    private IJSRuntime JS { get; set; }
  
}
