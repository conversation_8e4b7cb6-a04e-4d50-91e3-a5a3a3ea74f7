﻿@page "/StudentDetails/{studentid:int}"
@inject IGenericRepository<StudentDetailDto> studentDetailsRepo
@inject IGenericRepository<EnrollmentModel> enrollmentRepo
@inject IGenericRepository<DiscountModel> discountRepo
@inject IGenericRepository<ProgramModel> programRepo
@inject IGenericRepository<SectionModel> sectionRepo
@inject IGenericRepository<ShiftModel> shiftRepo
@inject IJSRuntime JS
@inject MessageServices MessageServices
@rendermode InteractiveServer
@inject NavigationManager NavigationManager
<PageTitle>Student Details</PageTitle>

@if (student == null)
{
    <div class="text-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p>Loading student details...</p>
    </div>
}
else
{
    <EditForm Model="this" OnValidSubmit="SaveAllAsync">
        <DataAnnotationsValidator />
        <div class="container mt-5 student-details-container">
            <div class="card shadow-lg border-0 rounded-4 p-4 bg-white">
                <!-- Student Edit Section -->
                <div class="text-center mb-4">
                    <label for="fileInput" class="pointer">
                        @if (!string.IsNullOrEmpty(student.ProfileImage))
                        {
                            <img src="@student.ProfileImage" class="profile-img-large rounded-circle shadow" />
                        }
                        else
                        {
                            <div class="no-image">Click to add image</div>
                        }
                    </label>
                    <InputFile id="fileInput" OnChange="HandleImageUpload" accept="image/*" style="display: none;" />
                    <h2 class="mt-3 mb-1">@student.Name @student.LastName</h2>
                    <p class="text-muted">Student ID: @StudentId</p>
                </div>

                <!-- Student Personal Details Form -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label>👤 Name</label>
                        <InputText @bind-Value="student.Name" class="form-control mb-2" />
                    </div>
                    <div class="col-md-6">
                        <label>👤 Last Name</label>
                        <InputText @bind-Value="student.LastName" class="form-control mb-2" />
                    </div>
                    <div class="col-md-6">
                        <label>👨 Father Name</label>
                        <InputText @bind-Value="student.FatherName" class="form-control mb-2" />
                    </div>
                    <div class="col-md-6">
                        <label>📞 Contact</label>
                        <InputText @bind-Value="student.Contact" class="form-control mb-2" />
                    </div>
                    <div class="col-md-6">
                        <label>🎂 Age</label>
                        <InputNumber @bind-Value="student.Age" class="form-control mb-2" />
                    </div>
                    <div class="col-md-6">
                        <label>🆔 Tazkera Number</label>
                        <InputText @bind-Value="student.TazkeraNumber" class="form-control mb-2" />
                    </div>
                    <div class="col-md-12">
                        <label>🏠 Address</label>
                        <InputTextArea @bind-Value="student.Address" class="form-control mb-2" rows="2" />
                    </div>
                </div>

                <hr />

                <!-- Enrollment Management Section -->
                <button type="button" class="btn btn-primary" @onclick="ToggleEnrollmentView">
                    @if (showEnrollments)
                    {
                        <i class="fas fa-eye-slash"></i> <span>Hide Enrollment</span>
                    }
                    else
                    {
                        <i class="fas fa-eye"></i> <span>Show Enrollments</span>
                    }
                </button>


                @if (showEnrollments)
                {
                    <!-- Add New Enrollment -->
                    <div class="border rounded-3 p-3 mb-3 bg-light">
                        <h6 class="mb-3">➕ Add New Enrollment</h6>
                        <div class="row g-2">
                            <div class="col-md-3">
                                <label>Program</label>
                                <InputSelect @bind-Value="newEnrollment.ProgramId" class="form-select">
                                    <option value="0">-- Select Program --</option>
                                    @foreach (var program in programs.Where(program=>program.IsActive ==1))
                                    {
                                        <option value="@program.ProgramId">@program.Name</option>
                                    }
                                </InputSelect>
                            </div>
                            <div class="col-md-3">
                                <label>Section</label>
                                <InputSelect @bind-Value="newEnrollment.SectionId" class="form-select">
                                    <option value="0">-- Select Section --</option>
                                    @foreach (var sect in sections)
                                    {
                                        <option value="@sect.SectionId">@sect.Name</option>
                                    }
                                </InputSelect>
                            </div>
                            <div class="col-md-3">
                                <label>Shift</label>
                                <InputSelect @bind-Value="newEnrollment.ShiftId" class="form-select">
                                    <option value="0">-- Select Shift --</option>
                                    @foreach (var shift in shifts)
                                    {
                                        <option value="@shift.ShiftId">@shift.Name</option>
                                    }
                                </InputSelect>
                            </div>
                            <div class="col-md-3">
                                <label>Enrollment Date</label>
                                <InputDate @bind-Value="newEnrollment.EnrollmentDate" class="form-control" />
                            </div>
                        </div>
                        <button type="button" class="btn btn-success mt-2" @onclick="AddEnrollment">
                            <i class="fas fa-plus"></i> Add Enrollment
                        </button>
                    </div>

                    <!-- Existing Enrollments -->
                    @if (enrollments != null && enrollments.Any())
                    {
                        @foreach (var enrollment in enrollments)
                        {
                            var program = programs.FirstOrDefault(p => p.ProgramId == enrollment.ProgramId);
                            var discount = discounts.FirstOrDefault(d => d.ProgramId == enrollment.ProgramId && d.StudentId == StudentId);

                            <div class="border rounded-3 p-3 mb-3 bg-light enrollment-card">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="text-primary mb-0">📖 @(program?.Name ?? "Unknown Program")</h6>
                                    <button type="button" class="btn btn-danger btn-sm" @onclick="() => RemoveEnrollment(enrollment.EnrollmentId)">
                                        <i class="fas fa-trash"></i> Remove
                                    </button>
                                </div>

                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <label>Program</label>
                                        <InputSelect @bind-Value="enrollment.ProgramId" class="form-select">
                                            @foreach (var prog in programs)
                                            {
                                                <option value="@prog.ProgramId">@prog.Name</option>
                                            }
                                        </InputSelect>
                                    </div>
								<div class="col-md-3">
									<label>Period</label>
									<InputNumber @bind-Value="enrollment.Period" class="form-control" disabled/>
                                    </div>
                                    <div class="col-md-3">
                                        <label>Section</label>
                                        <InputSelect @bind-Value="enrollment.SectionId" class="form-select">
                                            @foreach (var sect in sections)
                                            {
                                                <option value="@sect.SectionId">@sect.Name</option>
                                            }
                                        </InputSelect>
                                    </div>
                                    <div class="col-md-3">
                                        <label>Shift</label>
                                        <InputSelect @bind-Value="enrollment.ShiftId" class="form-select">
                                            @foreach (var shift in shifts)
                                            {
                                                <option value="@shift.ShiftId">@shift.Name</option>
                                            }
                                        </InputSelect>
                                    </div>
                                    <div class="col-md-3">
                                        <label>Enrollment Date</label>
                                        <InputDate @bind-Value="enrollment.EnrollmentDate" class="form-control" />
                                    </div>
                                </div>

                                <!-- Discount Information -->
                                @if (discount != null)
                                {
                                    <div class="mt-3 p-2 rounded bg-success bg-opacity-10 border border-success">
                                        <h6 class="text-success mb-2">💰 Discount Information</h6>
                                        <div class="row g-1">
                                            <div class="col-md-3">
                                                <small><strong>Discount:</strong> @discount.DiscountPercentage%</small>
                                            </div>
                                            <div class="col-md-3">
                                                <small><strong>Reason:</strong> @(string.IsNullOrEmpty(discount.Reason) ? "No reason" : discount.Reason)</small>
                                            </div>
                                            <div class="col-md-3">
                                                <small><strong>Applied:</strong> @discount.Created_At.ToString("MMM dd, yyyy")</small>
                                            </div>
                                            <div class="col-md-3">
                                                <span class="badge @(discount.IsActive == 1 ? "bg-success" : "bg-secondary")">
                                                    @(discount.IsActive == 1 ? "Active" : "Inactive")
                                                </span>
                                            </div>
                                        </div>
                                        @if (program != null && discount.IsActive == 1)
                                        {
                                            var discountAmount = (program.Fee * discount.DiscountPercentage) / 100;
                                            var finalFee = program.Fee - discountAmount;
                                            <div class="row g-1 mt-2">
                                                <div class="col-md-4">
                                                    <small><strong>Original Fee:</strong> @program.Fee.ToString("C")</small>
                                                </div>
                                                <div class="col-md-4">
                                                    <small class="text-success"><strong>Discount Amount:</strong> -@discountAmount.ToString("C")</small>
                                                </div>
                                                <div class="col-md-4">
                                                    <small class="fw-bold text-primary"><strong>Final Fee:</strong> @finalFee.ToString("C")</small>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        }
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> This student is not enrolled in any programs yet.
                        </div>
                    }
                }

                <!-- Save Button -->
                <div class="row mt-4">
                    <div class="text-center col-md-6">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> Save All Changes
                        </button>
                    </div>
                    <div class="text-center col-md-6">
                        <button class="btn btn-danger btn-lg" @onclick="ConfirmDelete">
                            <i class="fas fa-delete"></i> Delete Student
                        </button>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle"></i> @successMessage
                    </div>
                }

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-circle"></i> @errorMessage
                    </div>
                }
            </div>
        </div>
    </EditForm>
}

@code {
    [Parameter]
    public int StudentId { get; set; }

    private StudentDetailDto? student;
    private List<EnrollmentModel> enrollments = new();
    private List<DiscountModel> discounts = new();
    private List<ProgramModel> programs = new();
    private List<SectionModel> sections = new();
    private List<ShiftModel> shifts = new();
    private EnrollmentModel newEnrollment = new() { EnrollmentDate = DateTime.Today };

    private bool showEnrollments = false;
    private string? successMessage;
    private string? errorMessage;

    protected override async Task OnInitializedAsync()
    {
        await LoadStudentDetails();
        await LoadEnrollments();
        await LoadDiscounts();
        await LoadPrograms();
        await LoadSections();
        await LoadShifts();
    }

    private async Task LoadStudentDetails()
    {
        try
        {
            var studentList = await studentDetailsRepo.GetAllAsyncById("GetAllStudentDetails", new { });
            student = studentList.FirstOrDefault(s => s.StudentId == StudentId);

            if (student == null)
            {
                student = new StudentDetailDto
                {
                    StudentId = StudentId,
                    Name = "",
                    LastName = "",
                    FatherName = "",
                    Contact = "",
                    Age = 0,
                    TazkeraNumber = "",
                    Address = "",
                    ProfileImage = ""
                };
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading student details: {ex.Message}";
        }
    }

    private async Task LoadEnrollments()
    {
        try
        {
            enrollments = (await enrollmentRepo.GetAllAsyncById("GetEnrollmentByStudentAndProgram", new { StudentId })).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading enrollments: {ex.Message}");
        }
    }

    private async Task LoadDiscounts()
    {
        try
        {
            var allDiscounts = new List<DiscountModel>();

            foreach (var enrollment in enrollments)
            {
                var programDiscounts = await discountRepo.GetAllAsyncById("GetStudentDiscountByProgramAndStudent", 
                    new { ProgramId = enrollment.ProgramId, StudentId });
                allDiscounts.AddRange(programDiscounts);
            }

            discounts = allDiscounts;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading discounts: {ex.Message}");
        }
    }

    private async Task LoadPrograms()
    {
        try
        {
            programs = (await programRepo.GetAllAsync("GetAllPrograms")).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading programs: {ex.Message}");
        }
    }

    private async Task LoadSections()
    {
        try
        {
            sections = (await sectionRepo.GetAllAsync("GetAllSections")).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading sections: {ex.Message}");
        }
    }

    private async Task LoadShifts()
    {
        try
        {
            shifts = (await shiftRepo.GetAllAsync("GetAllShifts")).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading shifts: {ex.Message}");
        }
    }

    private void ToggleEnrollmentView()
    {
        showEnrollments = !showEnrollments;
    }

    private async Task AddEnrollment()
    {
        try
        {
            if (newEnrollment.ProgramId == 0 || newEnrollment.SectionId == 0 || newEnrollment.ShiftId == 0)
            {
                errorMessage = "Please select all required fields for enrollment.";
                return;
            }

            newEnrollment.StudentId = StudentId;
            await enrollmentRepo.AddAsync("EnrollStudent", new
            {
                newEnrollment.StudentId,
                newEnrollment.ProgramId,
                newEnrollment.SectionId,
                newEnrollment.ShiftId,
                newEnrollment.EnrollmentDate
            });

            newEnrollment = new() { EnrollmentDate = DateTime.Today };
            await LoadEnrollments();
            await LoadDiscounts();
            successMessage = "Enrollment added successfully!";
            errorMessage = null;
        }
        catch (Exception ex)
        {
            errorMessage = $"Error adding enrollment: {ex.Message}";
        }
    }

    private async Task RemoveEnrollment(int enrollmentId)
    {
        try
        {
            if (await JS.InvokeAsync<bool>("confirm", "Are you sure you want to remove this enrollment?"))
            {
                await enrollmentRepo.DeleteAsync("DeleteEnrollment", new { EnrollmentId = enrollmentId });
                await LoadEnrollments();
                await LoadDiscounts();
                successMessage = "Enrollment removed successfully!";
                errorMessage = null;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error removing enrollment: {ex.Message}";
        }
    }
    private async Task ConfirmDelete()
    {
        if (await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete this student?"))
        {
            await DeleteStudent();
        }
    }
    public async Task DeleteStudent()
    {
        try{
            await studentDetailsRepo.DeleteAsync("DeleteStudent", new { StudentId });
            successMessage = "Student deleted successfully!";
            errorMessage = null;
			MessageServices.Message = "Student deleted successfully!"; 
            NavigationManager.NavigateTo("student");

        }
		catch (Exception ex)
		{
			errorMessage = $"Error deleting student: {ex.Message}";
			return;
		}
    }
    private async Task SaveAllAsync()
    {
        try
        {
            // Save student details
            await studentDetailsRepo.UpdateAsync("UpdateStudentAndStudentDetails", new
            {
                student.StudentId,
                student.Name,
                student.LastName,
                student.FatherName,
                student.Contact,
                student.StudentDetailsId,
                student.Age,
                student.TazkeraNumber,
                student.Address,
                student.ProfileImage
            });

            // Save enrollment updates
            foreach (var enrollment in enrollments)
            {
                await enrollmentRepo.UpdateAsync("EditEnrollment", new
                {
                    enrollment.EnrollmentId,
                    enrollment.ProgramId,
                    enrollment.SectionId,
                    enrollment.ShiftId,
                   
                });
            }

            successMessage = "All changes saved successfully!";
            errorMessage = null;
        }
        catch (Exception ex)
        {
            errorMessage = $"Error saving changes: {ex.Message}";
            successMessage = null;
        }
    }

    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        try
        {
            var file = e.File;
            if (file != null)
            {
                var buffer = new byte[file.Size];
                await file.OpenReadStream().ReadAsync(buffer);
                
                // Convert to base64 for display
                var base64 = Convert.ToBase64String(buffer);
                student.ProfileImage = $"data:image/jpeg;base64,{base64}";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error uploading image: {ex.Message}";
        }
    }

    // DTOs and Models
    public class StudentDetailDto
    {
        public int StudentId { get; set; }
        public int StudentDetailsId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FatherName { get; set; } = string.Empty;
        public string Contact { get; set; } = string.Empty;
        public int Age { get; set; }
        public string ProfileImage { get; set; } = string.Empty;
        public string TazkeraNumber { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
    }

    public class EnrollmentModel
    {
        public int EnrollmentId { get; set; }
        public int SectionId { get; set; }
        public int StudentId { get; set; }
        public int ProgramId { get; set; }
		public int Period { get; set; } 
		public DateTime EnrollmentDate { get; set; } = DateTime.Today;
        public int ShiftId { get; set; }
    }

    public class SectionModel
    {
        public int SectionId { get; set; }
        public string Name { get; set; } = string.Empty;
    }

    public class ShiftModel
    {
        public int ShiftId { get; set; }
        public string Name { get; set; } = string.Empty;
    }
}

<style>
    .student-details-container {
        max-width: 1000px;
        margin: auto;
    }

    .profile-img-large {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border: 5px solid #dee2e6;
        background-color: #f8f9fa;
        cursor: pointer;
    }

    .no-image {
        width: 150px;
        height: 150px;
        line-height: 150px;
        text-align: center;
        border-radius: 50%;
        background-color: #e0e0e0;
        font-weight: bold;
        color: #6c757d;
        border: 5px dashed #ccc;
        margin: auto;
        cursor: pointer;
    }

    .enrollment-card {
        background-color: #f8f9fa;
        transition: 0.3s ease;
        border-left: 4px solid #0d6efd;
    }

    .enrollment-card:hover {
        background-color: #e9ecef;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .pointer {
        cursor: pointer;
    }

   
</style>