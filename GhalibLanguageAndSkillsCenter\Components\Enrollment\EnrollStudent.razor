﻿@page "/enroll"
@rendermode InteractiveServer
@inject IWebHostEnvironment Env
@using TabBlazor

@if (!string.IsNullOrEmpty(successMessage))
{
    <Alert BackgroundColor="TablerColor.Success" Title="Success" Dismissible>
        <div class="text-secondary">@successMessage</div>
    </Alert>
}

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger mt-3">
        @errorMessage
    </div>
}
<h3>Enroll Student</h3>

<EditForm Model="@enrollmentModel" OnValidSubmit="HandleEnroll" class="p-4 m-4 shadow-lg" FormName="enroll">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label>Name</label>
        <InputText class="form-control" @bind-Value="enrollmentModel.Name" />
    </div>

    <div class="form-group">
        <label>Last Name</label>
        <InputText class="form-control" @bind-Value="enrollmentModel.LastName" />
    </div>

    <div class="form-group">
        <label>Father Name</label>
        <InputText class="form-control" @bind-Value="enrollmentModel.FatherName" />
    </div>

    <div class="form-group">
        <label>Contact</label>
        <InputText class="form-control" @bind-Value="enrollmentModel.Contact" />
    </div>

    <div class="form-group">
        <label>Student Image</label>
        <InputFile OnChange="HandleImageUpload" class="form-control"/>
    </div>

    <div class="form-group">
        <label>Select Program</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram
         SelectedProgramId="@enrollmentModel.ProgramId"
         
         SelectedProgramIdChanged="OnProgramChange"
        />
    </div>

    <div class="form-group">
        <label>Select Section</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectSection 
    SelectedSectionId="@enrollmentModel.SectionId"
    SelectedSectionIdChanged="OnSectionChanged" />

    </div>

    <div class="form-group">
        <label>Select Shift</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectShift SelectedShiftId="@enrollmentModel.ShiftId"
                                                                                  SelectedShiftIdChanged="OnShiftChange" />

    </div>

    <div class="form-group">
        <label>Enrollment Date</label>
        <InputPersianDatePicker CssClass="form-control" @bind-Value="enrollDate"></InputPersianDatePicker>
    </div>

    <button class="btn btn-primary mt-4 w-50" type="submit">Enroll Student</button>
</EditForm>



