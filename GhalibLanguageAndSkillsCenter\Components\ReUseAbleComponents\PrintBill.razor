﻿@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject IJSRuntime jsRuntime
@rendermode InteractiveServer
@using Moraba



@if (PrintStudentBill.StudentId != 0)
{
    <div class="modal show d-block modal-backdrop-custom" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 bg-transparent shadow-none p-0">

                <div class="receipt-box p-4">
                    <!-- Header with Logo and Title -->
                    <div class="receipt-header mb-3 d-flex flex-row-reverse align-items-center justify-content-between">

                        <img src="/images/logo.png" alt="Logo" style="height: 60px;" />
                        <div class="text-center flex-fill fw-bold pe-4" style="font-size: 20px;">
                            <div>مرکز زبان و مهارت های غالب</div>
                            <div>آمریت مالی</div>
                            <div>رسید پرداخت پول</div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between  flex-wrap">
                        <div class="info-pair">
                            <span class="label">نام:</span>
                            <span class="value">@PrintStudentBill.StudentName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">ولد:</span>
                            <span class="value">@PrintStudentBill.FatherName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> آی دی:</span>
                            <span class="value">@PrintStudentBill.StudentIdNumber</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">پروگرام:</span>
                            <span class="value">@PrintStudentBill.ProgramName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">دوره:</span>
                            <span class="value"> @Moraba.Persian.Numbers.Convert.NumberToText(PrintStudentBill.Period.ToString())</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> رسید نمبر:</span>
                            <span class="value">@PrintStudentBill.BillNo</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> تاریخ پرداختی:</span>
                            <span class="value">@PersianDateConverter.ToPersianString(PrintStudentBill.PaymentDate)</span>

                        </div>
                    </div>


                    <!-- One-row Table -->
                    <table class="receipt-table w-100">
                        <tr>
                            <th class="label-cell"> فیس سمستر:</th>
                            <th class="label-cell">مبلغ پرداختی:</th>
                            <th class="label-cell"> بیلانس:</th>
                        </tr>
                        <tr>
                            

                          
                            <td class="value-cell">@PrintStudentBill.ProgramFee</td>

                          

                           
                            <td class="value-cell">@PrintStudentBill.PaidAmount.ToString("N0")</td>
                           
                            <td class="value-cell">@PrintStudentBill.RemainingAmount.ToString("N0")</td>
                        </tr>
                        <tr><td colspan="3">مبلغ پرداختی به حروف:   @Moraba.Persian.Numbers.Convert.NumberToText(PrintStudentBill.PaidAmount.ToString()) افغانی</td></tr>
                    </table>
                    <div class="d-flex justify-content-between mt-3 p-4">

                        <p class="signature-label">امضا پرداخت‌کننده:</p>
                        
                        <p class="signature-label">امضا دریافت‌کننده:</p>
                        
                    </div>

                    <!-- Footer Buttons -->
                    <div class="receipt-footer mt-4 no-print text-center">
                        <button class="btn btn-secondary mx-2" @onclick="CloseModal">بند</button>
                        <button class="btn btn-primary mx-2" @onclick="PrintNow">
                            <i class="bi bi-printer me-1"></i> چاپ
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public StudentPaymentModel PrintStudentBill { get; set; } = new();

    [Parameter]
    public EventCallback OnClose { get; set; }
   
    private async Task PrintNow()
    {
        await jsRuntime.InvokeVoidAsync("window.print");
    }

    private async Task CloseModal()
    {
        PrintStudentBill = new();
        await OnClose.InvokeAsync(null);
    }
}
<style>
   

</style>
