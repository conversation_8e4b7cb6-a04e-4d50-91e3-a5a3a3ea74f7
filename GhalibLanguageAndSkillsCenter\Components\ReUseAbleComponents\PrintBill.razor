@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject IJSRuntime jsRuntime
@rendermode InteractiveServer
@using Moraba

@if (PrintStudentBill.StudentId != 0)
{
    <div class="modal show d-block modal-backdrop-custom" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 bg-transparent shadow-none p-0">

                <div id="printable-receipt" class="receipt-box p-4">
                    <!-- Header with Logo and Title -->
                    <div class="receipt-header mb-3 d-flex flex-row-reverse align-items-center justify-content-between">
                        <img src="/images/logo.png" alt="Logo" style="height: 60px;" />
                        <div class="text-center flex-fill fw-bold pe-4" style="font-size: 20px;">
                            <div>مرکز زبان و مهارت های غالب</div>
                            <div>آمریت مالی</div>
                            <div>رسید پرداخت پول</div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between flex-wrap">
                        <div class="info-pair">
                            <span class="label">نام:</span>
                            <span class="value">@PrintStudentBill.StudentName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">ولد:</span>
                            <span class="value">@PrintStudentBill.FatherName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> آی دی:</span>
                            <span class="value">@PrintStudentBill.StudentIdNumber</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">پروگرام:</span>
                            <span class="value">@PrintStudentBill.ProgramName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">دوره:</span>
                            <span class="value"> @Moraba.Persian.Numbers.Convert.NumberToText(PrintStudentBill.Period.ToString())</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> رسید نمبر:</span>
                            <span class="value">@PrintStudentBill.BillNo</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> تاریخ پرداختی:</span>
                            <span class="value">@PersianDateConverter.ToPersianString(PrintStudentBill.PaymentDate)</span>
                        </div>
                    </div>

                    <!-- One-row Table -->
                    <table class="receipt-table w-100">
                        <tr>
                            <th class="label-cell"> فیس سمستر:</th>
                            <th class="label-cell">مبلغ پرداختی:</th>
                            <th class="label-cell"> بیلانس:</th>
                        </tr>
                        <tr>
                            <td class="value-cell">@PrintStudentBill.ProgramFee</td>
                            <td class="value-cell">@PrintStudentBill.PaidAmount.ToString("N0")</td>
                            <td class="value-cell">@PrintStudentBill.RemainingAmount.ToString("N0")</td>
                        </tr>
                        <tr>
                            <td colspan="3">مبلغ پرداختی به حروف: @Moraba.Persian.Numbers.Convert.NumberToText(PrintStudentBill.PaidAmount.ToString()) افغانی</td>
                        </tr>
                    </table>
                    
                    <div class="d-flex justify-content-between mt-3 p-4">
                        <p class="signature-label">امضا پرداخت‌کننده:</p>
                        <p class="signature-label">امضا دریافت‌کننده:</p>
                    </div>

                    <!-- Footer Buttons -->
                    <div class="receipt-footer mt-4 no-print text-center">
                        <button class="btn btn-secondary mx-2" @onclick="CloseModal">بند</button>
                        <button class="btn btn-primary mx-2" @onclick="PrintNow">
                            <i class="bi bi-printer me-1"></i> چاپ
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public StudentPaymentModel PrintStudentBill { get; set; } = new();

    [Parameter]
    public EventCallback OnClose { get; set; }
   
    private async Task PrintNow()
    {
        await jsRuntime.InvokeVoidAsync("printReceipt");
    }

    private async Task CloseModal()
    {
        PrintStudentBill = new();
        await OnClose.InvokeAsync(null);
    }
}

<script>
    window.printReceipt = () => {
        // Get the receipt content
        const receiptContent = document.getElementById('printable-receipt');
        
        if (!receiptContent) {
            console.error('Receipt content not found');
            return;
        }

        // Create a new window for printing
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        // Write the content to the new window
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Receipt</title>
                <meta charset="utf-8">
                <style>
                    body {
                        font-family: 'Tahoma', Arial, sans-serif;
                        direction: rtl;
                        margin: 20px;
                        color: black;
                        background: white;
                    }
                    
                    .receipt-box {
                        background: white;
                        border: 2px solid black;
                        border-radius: 8px;
                        padding: 20px;
                        max-width: 800px;
                        margin: 0 auto;
                        color: black;
                    }
                    
                    .receipt-header {
                        border-bottom: 2px solid black;
                        padding-bottom: 15px;
                        margin-bottom: 15px;
                        display: flex;
                        flex-direction: row-reverse;
                        align-items: center;
                        justify-content: space-between;
                    }
                    
                    .text-center {
                        text-align: center;
                        font-weight: bold;
                        font-size: 20px;
                    }
                    
                    .d-flex {
                        display: flex;
                    }
                    
                    .justify-content-between {
                        justify-content: space-between;
                    }
                    
                    .flex-wrap {
                        flex-wrap: wrap;
                    }
                    
                    .info-pair {
                        display: flex;
                        align-items: center;
                        margin-bottom: 8px;
                        font-size: 16px;
                        font-weight: 600;
                        gap: 4px;
                        min-width: 200px;
                    }
                    
                    .label {
                        color: black;
                        font-weight: bold;
                    }
                    
                    .value {
                        color: black;
                    }
                    
                    .receipt-table {
                        border-collapse: collapse;
                        width: 100%;
                        margin: 20px 0;
                        border: 2px solid black;
                    }
                    
                    .receipt-table td,
                    .receipt-table th {
                        padding: 10px;
                        vertical-align: middle;
                        font-size: 16px;
                        border: 1px solid black;
                        text-align: center;
                    }
                    
                    .label-cell {
                        font-weight: bold;
                        background-color: #f5f5f5;
                    }
                    
                    .value-cell {
                        font-weight: bold;
                    }
                    
                    .signature-label {
                        font-weight: bold;
                        border-top: 1px solid black;
                        padding-top: 10px;
                        margin: 0;
                    }
                    
                    .no-print {
                        display: none;
                    }
                    
                    img {
                        max-height: 60px;
                        width: auto;
                    }
                    
                    @page {
                        margin: 0.5in;
                        size: A4;
                    }
                </style>
            </head>
            <body>
                ${receiptContent.outerHTML}
            </body>
            </html>
        `);
        
        // Close the document and print
        printWindow.document.close();
        
        // Wait for content to load then print
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    };
</script>

<style>
    .modal-backdrop-custom {
        background-color: rgba(0, 0, 0, 0.4);
    }

    .receipt-box {
        background-color: #fdeef2;
        border: 1px solid #aaa;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        width: 800px;
        direction: rtl;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: #1a1a4a;
        margin: auto;
    }

    .receipt-header {
        border-bottom: 1px dashed #1a1a4a;
        padding-bottom: 8px;
    }

    .receipt-table {
        border-collapse: collapse;
        width: 100%;
    }

    .receipt-table td,
    .receipt-table th {
        padding: 8px 10px;
        vertical-align: middle;
        font-size: 17px;
        border: 1px dotted blue;
    }

    .label-cell {
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
    }

    .value-cell {
        text-align: left;
        white-space: nowrap;
    }

    .info-pair {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
        gap: 4px;
        min-width: 200px;
    }

    .label {
        color: #1a1a4a;
    }

    .value {
        color: #1a1a4a;
    }

    .signature-label {
        font-weight: 500;
        text-align: right;
        white-space: nowrap;
    }
</style>