﻿@page "/payment"
@using GhalibLanguageAndSkillsCenter.Models.Payment
@using System.Globalization
@inject IGenericRepository<StudentModel> StudentRepo
@inject IGenericRepository<ProgramModel> ProgramRepo
@inject IGenericRepository<DiscountModel> DiscountRepo
@inject IGenericRepository<StudentPaymentModel> PaymentRepo
@inject IGenericRepository<GeneralDiscounModel> GeneralDiscountRepo
@rendermode InteractiveServer
@attribute [Authorize]

@if (!string.IsNullOrEmpty(message))
{
    <div class="alert alert-warning alert-dismissible">
        <i class="fas fa-check-circle"></i> @message
    </div>
}
<div class="shadow-lg p-4 billform">
    <h3>Payment</h3>

    <div class="row mb-3">
        <div class="col-md-6">
            <!-- Program Selection -->
            <InputSelect class="form-control"
            Value="SelectedProgramId"
            ValueChanged="@(async (int id) => await OnProgramChanged(id))"
            ValueExpression="() => SelectedProgramId">
                <option value="0">-- Select program --</option>
                @foreach (var program in programs)
                {
                    <option value="@program.ProgramId">@program.Name</option>
                }
            </InputSelect>
        </div>

        <div class="col-md-6">
            <!-- Student Selection -->
            <InputSelect class="form-control"
            @bind-Value="SelectedStudentId"
            disabled="@(students == null || students.Count == 0)">
                <option value="0">-- Select student --</option>
                @if (students != null && students.Count == 0 && SelectedProgramId > 0)
                {
                    <option disabled>-- No students available --</option>
                }
                else
                {
                    @foreach (var student in students)
                    {
                        <option value="@student.StudentId">@student.Name @student.LastName</option>
                    }
                }
            </InputSelect>
        </div>
    </div>

    @if (SelectedProgramId != 0 && SelectedStudentId != 0)
    {
        <div class="table-responsive mb-3">
            <table class="table align-middle text-center">
                <thead class="table-light">
                    <tr>
                        <th>Student Discount (%)</th>
                        <th>Program Discount (%)</th>
                        <th>Total Discount (%)</th>
                        <th>Total Fee</th>
                        <th>Final Fee</th>
                        <th>Total Paid</th>
                        <th>Remaining Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="text-warning fw-bold">@studentdiscount</span></td>
                        <td><span class="text-warning fw-bold">@ProgramDiscount</span></td>
                        <td><span class="text-warning fw-bold">@TotalDiscount</span></td>
                        <td><span class="text-info fw-semibold">@totalFee</span></td>
                        <td><span class="text-info fw-semibold">@FinalFee</span></td>
                        <td><span class="text-success fw-bold">@paymentInfo.TotalPaid</span></td>
                        <td><span class="text-danger fw-bold">@paymentInfo.RemainingAmount</span></td>
                    </tr>
                </tbody>
            </table>
        </div>




        <!-- Payment Form -->
        <EditForm Model="paymentModel" OnValidSubmit="HandleValidSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />

            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">Pay Amount</label>
                    <InputNumber @bind-Value="paymentModel.PaidAmount"
                    class="form-control" />
                </div>
                <div class="col-md-4">
                    <label class="form-label">Payment Date</label>
                    <InputPersianDatePicker CssClass="form-control" @bind-Value="paymentDate" ></InputPersianDatePicker>
                </div>
                <div class="col-md-4 align-self-end">
                    <button type="submit" class="btn btn-primary">Save Payment</button>
                </div>
            </div>
        </EditForm>

        @if (!string.IsNullOrEmpty(saveMessage))
        {
            <div class="alert alert-info">@saveMessage</div>
        }
    }
</div>
@if(SelectedStudentId !=0)
{

    <div class="shadow-lg p-4 mt-4 bilhistory">
        @if (paymentDetails == null)
        {
            <p>Loading…</p>
        }
        else if (!paymentDetails.Any())
        {
            <p><em>No payment history found.</em></p>
        }
        else
        {
            <!-- ── ROW: table on the left (8 cols), profile on the right (4 cols) -->
            <div class="row">
                <!-- RIGHT: Profile Card (image full-width, colored name bar underneath) -->
                <div class="col-lg-4 d-flex justify-content-center">
                    <div class="card shadow-sm border-0 mb-3" style="width: 100%; max-width: 320px; border-radius: 16px;">
                        @if (!string.IsNullOrEmpty(paymentDetails.First().ProfileImage))
                        {
                            <!-- Profile Image with rounded top corners -->
                            <img src="@paymentDetails.First().ProfileImage"
                            class="card-img-top"
                            alt="Student Profile"
                            style="border-top-left-radius: 16px; border-top-right-radius: 16px; object-fit: cover; height: 220px;" />
                        }
                        else
                        {
                            <!-- Placeholder -->
                            <div class="bg-secondary d-flex align-items-center justify-content-center"
                            style="height: 220px; border-top-left-radius: 16px; border-top-right-radius: 16px; color: white;">
                                <span>No Profile</span>
                            </div>
                        }

                        <div class="card-body text-center p-3">
                            <h5 class="text-primary fw-bold mb-1">@paymentDetails.First().StudentName</h5>
                        </div>


                    </div>
                </div>



                <!-- LEFT: Payment Table -->
                <div class="col-lg-8">
                    <div class="card mb-3">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Payment History</h5>
                            <h5 class="mb-0 text-success">Total Paid: @totalPaid</h5>
                        </div>

                        <div class="card-body p-0">
                            <table class="table table-striped table-hover mb-0">
                                <thead class="thead-light">
                                    <tr>

                                        <th class="text-center">Installment</th>       <!-- emphasized -->
                                        <th class="text-center">Date</th>              <!-- emphasized -->
                                        <th>Program</th>

                                        <th class="text-end">Paid</th>
                                        <th class="text-end">Print</th>    <!-- emphasized -->

                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in paymentDetails)
                                    {
                                        <tr>

                                            <td class="text-center">
                                                <span class="badge bg-primary">@item.Installment</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="text-muted">
                                                    @PersianDateConverter.ToPersianString(item.PaymentDate)
                                                </span>
                                            </td>
                                            <td>@item.ProgramName</td>

                                            <td class="text-end">
                                                <span class="text-success">@item.PaidAmount.ToString("N0")</span>
                                            </td>
                                            <td><button @onclick="()=>HandlePrint(item)" class="btn btn-dark">Print</button></td>

                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>


        }
    </div>


    <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.PrintBill PrintStudentBill="PrintingBill"  OnClose="HandleClose"/>

}

@code {
    private int SelectedProgramId { get; set; }
	private string paymentDate { get; set; } = PersianDateConverter.ToPersianString(DateTime.Today);
    private string? message;
    private int _selectedStudentId;
    public int SelectedStudentId
    {
        get => _selectedStudentId;
        set
        {
            if (_selectedStudentId == value) return;
            _selectedStudentId = value;
            _ = OnStudentChanged(value);
        }
    }

    private List<ProgramModel> programs = new();
    private List<StudentModel> students = new();
    private DiscountModel? discount;
    private string? studentdiscount;
    private int totalFee;
    private List<StudentPaymentModel> paymentDetails = new();
    private StudentPaymentModel paymentInfo = new();
    private string? FinalFee;
    private int LoadHistory = 0;
    private string? ProgramDiscount;
    private decimal TotalDiscount;
    private string totalPaid;

    public StudentPaymentModel PrintingBill = new();
    // New payment form model and message
    private StudentPaymentModel paymentModel = new();
    private string saveMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        programs = (await ProgramRepo.GetAllAsync("GetAllPrograms"))
           .Where(p => p.IsActive ==1)
           .ToList();

    }
    private void HandleClose()
    {
        PrintingBill = new();
    }
    private async Task OnProgramChanged(int programId)
    {
        SelectedProgramId = programId;
        TotalDiscount = 0;
        students = programId > 0
            ? (await StudentRepo.GetAllAsyncById("GetAllStudentsByProgram", new { ProgramId = programId })).ToList()
            : new List<StudentModel>();

        _selectedStudentId = 0;
        discount = null;
        studentdiscount = null;
        totalFee = programs.FirstOrDefault(p => p.ProgramId == programId)?.Fee ?? 0;

        GeneralDiscounModel general = new();

        general = await (GeneralDiscountRepo.GetByIdAsync("GetProgramDiscount", new { programId = SelectedProgramId }));
        if(general != null)
        {

            TotalDiscount = general.DiscountPercentage;
            ProgramDiscount = general.DiscountPercentage.ToString();
        }
        else{
            ProgramDiscount = "No Active Discount";
        }
        paymentInfo = new StudentPaymentModel();
        saveMessage = string.Empty;

        StateHasChanged();
    }
    private void HandlePrint (StudentPaymentModel item)
    {
        PrintingBill = item;

    }

    private async Task OnStudentChanged(int studentId)
    {
        SelectedStudentId = studentId;
        if (studentId > 0)
        {
            discount = await DiscountRepo.GetByIdAsync("GetStudentDiscount", new { studentid = studentId,ProgramId=SelectedProgramId });
            if(discount != null && discount.IsActive == 1)
            {
                TotalDiscount = (decimal.TryParse(ProgramDiscount, out var pd) ? pd : 0m)
                          + discount.DiscountPercentage;

                studentdiscount = discount?.DiscountPercentage.ToString();
            }
            else
            {
                FinalFee = totalFee.ToString();
                studentdiscount = "No Active Discount";
            }
            FinalFee = (totalFee - (totalFee * TotalDiscount / 100)).ToString();

            paymentDetails = (await PaymentRepo
                        .GetAllAsyncById("GetStudentPaymentHistory", new { studentId }))
                        .ToList();
            if (paymentDetails.Any())
            {
                totalPaid = paymentDetails[0].TotalPaid.ToString("N0");
            }
            else
            {
                totalPaid = "0";
            }
            paymentInfo = paymentDetails
                .Where(p => p.ProgramId == SelectedProgramId)
                .FirstOrDefault()
              ?? new StudentPaymentModel();

            // Initialize the paymentModel defaults
            paymentModel = new StudentPaymentModel
                {
                    StudentId = studentId,
                    ProgramId = SelectedProgramId,
                    DiscountPercentage = discount?.DiscountPercentage ?? 0,
                    RemainingAmount = paymentInfo.RemainingAmount
                };
        }
        else
        {
            discount = null;
            studentdiscount = null;
            paymentInfo = new StudentPaymentModel();
        }
        saveMessage = string.Empty;
        StateHasChanged();
    }

    private async Task HandleValidSubmit()
    {
        paymentModel.PaymentDate = PersianDateConverter.ToDateTime(paymentDate);
        paymentModel.BillNo = GenerateBillNumber();
        // Call stored procedure AddStudentPayment
        var outputParams = new Dictionary<string, object?>
        {
            { "@StudentId", SelectedStudentId },
            { "@ProgramId", SelectedProgramId },
            { "@PaidAmount", paymentModel.PaidAmount },
            { "@DiscountPercentage", paymentModel.DiscountPercentage },
            { "@PaymentDate", paymentModel.PaymentDate },
            {"@BillNo",paymentModel.BillNo},
            { "@RemainingAmount", 0 },
            { "@ReturnCode", 0 }
        };
        if(paymentModel.PaidAmount>0 )
        {

            await PaymentRepo.AddAsync("AddStudentPayment", outputParams);
			message = "Payment saved successfully.";
        }
        else{
            message = "The paid amount should be greater than 0";
        }


        // Check return code and remaining
        var returnCode = (int)outputParams["@ReturnCode"]!;
        var remaining = (int)outputParams["@RemainingAmount"]!;
        saveMessage = returnCode == 0
            ? $"Payment saved successfully. Remaining amount: {remaining}."
            : $"Overpayment: you can only pay up to {remaining}.";
        LoadHistory++;

        // Refresh student data
        await OnStudentChanged(SelectedStudentId);


    }
    private string GenerateBillNumber()
    {
        var pc = new PersianCalendar();
        int year = pc.GetYear(DateTime.Now);
        string yearPart = (year % 100).ToString("D2");
        string randomPart = new Random().Next(0, 100000).ToString("D5");
        return yearPart + randomPart;
    }
    public class GeneralDiscounModel
    {
        public int GeneralDiscountId { get; set; }
        public string Title { get; set; }
        public decimal DiscountPercentage { get; set; }
        public int ProgramId { get; set; }

    }
}
