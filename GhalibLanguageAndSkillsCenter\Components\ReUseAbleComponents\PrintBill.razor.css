﻿.modal-backdrop-custom {
    background-color: rgba(0, 0, 0, 0.4);
}

.receipt-box {
    background-color: #fdeef2;
    border: 1px solid #aaa;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 800px;
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #1a1a4a;
    margin: auto;
}

.receipt-header {
    border-bottom: 1px dashed #1a1a4a;
    padding-bottom: 8px;
}

.receipt-table {
    border-collapse: collapse;
    width: 100%;
}

    .receipt-table td, .receipt-table th {
        padding: 8px 10px;
        vertical-align: middle;
        font-size: 17px;
        border: 1px dotted blue;
    }

    .receipt-table .no-border td {
        padding-top: 20px;
    }

.label-cell {
    font-weight: 600;
    text-align: right;
    white-space: nowrap;
}

.value-cell {
    text-align: left;
    white-space: nowrap;
}

.signature-label {
    font-weight: 500;
    text-align: right;
    white-space: nowrap;
}

.signature-line {
    border-bottom: 1px solid #1a1a4a;
    width: 150px;
}

.receipt-footer .btn {
    width: 100px;
}

.info-pair {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
    gap: 4px;
    min-width: 200px;
}

.label {
    color: #1a1a4a;
}

.value {
    color: #1a1a4a;
}
@media print {
    body * {
        visibility: hidden;
    }
    .modal {
        visibility: visible;
        position: absolute;

    }
    .receipt-box, .receipt-box * {
        visibility: visible;
    }

    .receipt-box {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

   
}
