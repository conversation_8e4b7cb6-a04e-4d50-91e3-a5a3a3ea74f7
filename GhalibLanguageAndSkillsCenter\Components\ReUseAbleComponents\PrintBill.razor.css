﻿@media print {

    /* Show only the receipt box and its contents */
    .receipt-box,
    .receipt-box * {
        visibility: visible !important;
    }

    /* Force the receipt to be positioned at the top-left of the printed page */
    .receipt-box {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        margin: 0;
        padding: 0;
    }

    /* Optionally hide the backdrop so only the receipt content prints */
    .modal-backdrop-custom {
        display: none !important;
    }

    /* Ensure any .no-print elements (like your footer buttons) are hidden */
    .no-print {
        display: none !important;
    }
}

/* (Existing styles below) */
.modal-backdrop-custom {
    background-color: rgba(0, 0, 0, 0.4);
}

.receipt-box {
    background-color: #fdeef2;
    border: 1px solid #aaa;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 800px;
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #1a1a4a;
    margin: auto;
}

.receipt-header {
    border-bottom: 1px dashed #1a1a4a;
    padding-bottom: 8px;
}

.receipt-table {
    border-collapse: collapse;
    width: 100%;
}

    .receipt-table td,
    .receipt-table th {
        padding: 8px 10px;
        vertical-align: middle;
        font-size: 17px;
        border: 1px dotted blue;
    }

.label-cell {
    font-weight: 600;
    text-align: right;
    white-space: nowrap;
}

.value-cell {
    text-align: left;
    white-space: nowrap;
}

.signature-label {
    font-weight: 500;
    text-align: right;
    white-space: nowrap;
}

.signature-line {
    border-bottom: 1px solid #1a1a4a;

    @media print {
        /* 1) Hide everything on the page */
        body * {
            display: none !important;
        }

        /* 2) Hide the dark backdrop so it never prints */
        .modal-backdrop-custom {
            display: none !important;
        }

        /* 3) Show the receipt box and all its descendants */
        .receipt-box,
        .receipt-box * {
            display: initial !important;
        }

        /* 4) Force the receipt box to sit at the top left of the print page */
        .receipt-box {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            margin: 0;
            padding: 0;
        }

        /* 5) Hide any .no-print elements (e.g. the “Close”/“Print” buttons) */
        .no-print {
            display: none !important;
        }
    }
    /* ===========================
       EXISTING STYLES (UNCHANGED)
       =========================== */
    .modal-backdrop-custom

{
    background-color: rgba(0, 0, 0, 0.4);
}

.receipt-box {
    background-color: #fdeef2;
    border: 1px solid #aaa;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 800px;
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #1a1a4a;
    margin: auto;
}

.receipt-header {
    border-bottom: 1px dashed #1a1a4a;
    padding-bottom: 8px;
}

.receipt-table {
    border-collapse: collapse;
    width: 100%;
}

    .receipt-table td,
    .receipt-table th {
        padding: 8px 10px;
        vertical-align: middle;
        font-size: 17px;
        border: 1px dotted blue;
    }

.label-cell {
    font-weight: 600;
    text-align: right;
    white-space: nowrap;
}

.value-cell {
    text-align: left;
    white-space: nowrap;
}

.signature-label {
    font-weight: 500;
    text-align: right;
    white-space: nowrap;
}

.signature-line {
    border-bottom: 1px solid #1a1a4a;
    width: 150px;
}

.receipt-footer .btn {
    width: 100px;
}

.info-pair {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
    gap: 4px;
    min-width: 200px;
}

.label {
    color: #1a1a4a;
}

.value {
    color: #1a1a4a;
}

width: 150px;
}

.receipt-footer .btn {
    width: 100px;
}

.info-pair {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
    gap: 4px;
    min-width: 200px;
}

.label {
    color: #1a1a4a;
}

.value {
    color: #1a1a4a;
}
