﻿@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <!-- Add meta tags and other head content if needed -->
</head>

<body>
    <div class="page">
        <!-- Blazorise Sidebar -->
        <NavMenu />

        <!-- Main Content Area -->
        <main class="main-content">
            <div class="myheader">
                <div class="top-row px-4">
                    <AuthorizeView>
                        <Authorized>
                            <div class="nav-item me-3">
                                <NavLink class="nav-link text-danger" href="Account/Logout">
                                    <span class="bi bi-box-arrow-right text-danger" aria-hidden="true"></span>
                                    خروج
                                </NavLink>
                            </div>
                        </Authorized>
                        <NotAuthorized>
                            <div class="nav-item px-3">
                                <NavLink class="nav-link" href="Account/Login">
                                    <span class="bi bi-person-badge-nav-menu" aria-hidden="true"></span>
                                    ورود
                                </NavLink>
                            </div>
                        </NotAuthorized>
                    </AuthorizeView>
                </div>
            </div>
            <article class="content px-4">
                @Body
            </article>
        </main>
    </div>

    <div id="blazor-error-ui" data-nosnippet>
        یک خطای ناشناخته رخ داده است.
        <a href="." class="reload">بارگذاری مجدد</a>
        <span class="dismiss">🗙</span>
    </div>
</body>
</html>

<style>
    .main-content {
        margin-right: 280px;
        min-height: 100vh;
        transition: margin-right 0.3s ease;
    }

    @media (max-width: 768px) {
        .main-content

    {
        margin-right: 0;
    }

    }

    .myheader {
        background: white;
        border-bottom: 1px solid #dee2e6;
        padding: 0.5rem 0;
    }

    .top-row {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        height: 60px;
    }
</style>