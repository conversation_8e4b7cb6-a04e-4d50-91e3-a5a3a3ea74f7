﻿@inject IGenericRepository<StaffModel> staffRepository
@inject IGenericRepository<StaffEducationModel> staffeducationRepository
@inject IGenericRepository<StaffEducationFeild> staffeducationfeildRepository
@inject IGenericRepository<StaffDegreeModel> staffeducationdegreeRepository
@inject IGenericRepository<UniversityModel> universityRepository
@rendermode InteractiveServer
@if (IsVisible)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);" @onclick="OnBackdropClick">
        <div class="modal-dialog modal-lg modal-dialog-centered" @onclick:stopPropagation>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Staff Education</h5>
                    <button type="button" class="btn-close" aria-label="Close" @onclick="Close"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="@newEducation" OnValidSubmit="HandleValidSubmit" FormName="StaffEducation">
                        <DataAnnotationsValidator />
                        <ValidationSummary />

                        <div class="row g-3">
                            <!-- Staff -->
                            <div class="col-md-6">
                                <label class="form-label">Staff</label>
                                <InputSelect class="form-select" @bind-Value="newEducation.StaffId">
                                    <option value="0">-- Select Staff --</option>
                                    @foreach (var staff in staffList)
                                    {
                                        <option value="@staff.StaffId">@staff.Name @staff.LastName</option>
                                    }
                                </InputSelect>
                            </div>

                            <!-- Education Field -->
                            <div class="col-md-6">
                                <label class="form-label">Education Field</label>
                                <InputSelect class="form-select" @bind-Value="newEducation.EducationFeild">
                                    <option value="0">-- Select Field --</option>
                                    @foreach (var field in fieldList)
                                    {
                                        <option value="@field.EducationFeildId">@field.EnglishName</option>
                                    }
                                </InputSelect>
                            </div>

                            <!-- Degree -->
                            <div class="col-md-6">
                                <label class="form-label">Degree</label>
                                <InputSelect class="form-select" @bind-Value="newEducation.EducationDegreeId">
                                    <option value="0">-- Select Degree --</option>
                                    @foreach (var degree in degreeList)
                                    {
                                        <option value="@degree.EducationDegreeId">@degree.EnglishName</option>
                                    }
                                </InputSelect>
                            </div>

                            <!-- University -->
                            <div class="col-md-6">
                                <label class="form-label">University</label>
                                <InputSelect class="form-select" @bind-Value="newEducation.UniversityId">
                                    <option value="0">-- Select University --</option>
                                    @foreach (var uni in universityList)
                                    {
                                        <option value="@uni.UniversityId">@uni.EnglishName</option>
                                    }
                                </InputSelect>
                            </div>

                            <div class="col-md-6">
                                <label>Education Starting Date</label>
                                <InputDate class="form-control" @bind-Value="newEducation.EducationStartingDate" />
                            </div>
                            <div class="col-md-6">
                                <label>Education Ending Date</label>
                                <InputDate class="form-control" @bind-Value="newEducation.EducationEndingDate" />
                            </div>
                            <div class="col-md-6">
                                <label>Grading Scale</label>
                                <InputNumber class="form-control" @bind-Value="newEducation.GradingScale" />
                            </div>

                            <!-- Submit and Cancel Buttons -->
                            <div class="col-12 d-flex justify-content-end gap-2 mt-3">
                                <button type="submit" class="btn btn-primary">Submit</button>
                                <button type="button" class="btn btn-secondary" @onclick="Close">Cancel</button>
                            </div>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public bool IsVisible { get; set; }

    [Parameter]
    public EventCallback<bool> IsVisibleChanged { get; set; }

    private StaffEducationModel newEducation = new();
    private List<StaffModel> staffList = new();
    private List<StaffEducationFeild> fieldList = new();
    private List<StaffDegreeModel> degreeList = new();
    private List<UniversityModel> universityList = new();

    protected override async Task OnInitializedAsync()
    {
        staffList = (await staffRepository.GetAllAsync("GetAllStaff")).ToList();
        fieldList = (await staffeducationfeildRepository.GetAllAsync("GetAllEducationFeilds")).ToList();
        degreeList = (await staffeducationdegreeRepository.GetAllAsync("GetAllEducationDegrees")).ToList();
        universityList = (await universityRepository.GetAllAsync("GetAllUniversities")).ToList();
    }

    private async Task HandleValidSubmit()
    {
        if (newEducation.StaffId == 0 || newEducation.EducationFeild == 0 || newEducation.EducationDegreeId == 0 || newEducation.UniversityId == 0)
        {
            // You can add validation message logic here if needed
            return;
        }
        await staffeducationRepository.AddAsync("CreateStaffEducation", new
        {
            EducationFeild = newEducation.EducationFeild,
            EducationDegreeId = newEducation.EducationDegreeId,
            UniversityId = newEducation.UniversityId,
            StaffId = newEducation.StaffId,
            EducationStartingDate = newEducation.EducationStartingDate,
            EducationEndingDate = newEducation.EducationEndingDate,
            GradingScale = newEducation.GradingScale
        });

        newEducation = new StaffEducationModel(); // Reset form
        await Close();
    }

    private async Task Close()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
    }

    private async Task OnBackdropClick()
    {
        await Close();
    }
}
