﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<div class="sidebar-nav">
    <!-- Brand -->
    <div class="sidebar-brand">
        <Anchor To="/" Class="brand-link">
            <Icon Name="IconName.School" Class="me-2" />
            <span class="brand-text">مرکز زبان و مهارت‌های غالب</span>
        </Anchor>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-menu">
        <ul class="nav flex-column">

            <!-- Dashboard -->
            <li class="nav-item">
                <NavLink class="nav-link sidebar-link" href="/" Match="NavLinkMatch.All">
                    <Icon Name="IconName.Dashboard" Class="sidebar-icon" />
                    <span>داشبورد</span>
                </NavLink>
            </li>

            <!-- Enrollment Section -->
            <li class="nav-item">
                <Dropdown>
                    <DropdownToggle Color="Color.None" Class="nav-link sidebar-link dropdown-toggle-custom">
                        <Icon Name="IconName.UserPlus" Class="sidebar-icon" />
                        <span>پذیرش</span>
                        <Icon Name="IconName.ChevronDown" Class="dropdown-arrow" />
                    </DropdownToggle>
                    <DropdownMenu Class="sidebar-dropdown">
                        <DropdownItem>
                            <NavLink class="dropdown-link" href="/enroll">
                                <Icon Name="IconName.CreditCard" Class="sidebar-icon" />
                                <span>ثبت نام</span>
                            </NavLink>
                        </DropdownItem>
                        <DropdownItem>
                            <NavLink class="dropdown-link" href="/student">
                                <Icon Name="IconName.Users" Class="sidebar-icon" />
                                <span>شاگردان</span>
                            </NavLink>
                        </DropdownItem>
                        <DropdownItem>
                            <NavLink class="dropdown-link" href="/home">
                                <Icon Name="IconName.Home" Class="sidebar-icon" />
                                <span>تمام ثبت نام ها</span>
                            </NavLink>
                        </DropdownItem>
                    </DropdownMenu>
                </Dropdown>
            </li>

            <!-- Finance Section -->
            <li class="nav-item">
                <Dropdown>
                    <DropdownToggle Color="Color.None" Class="nav-link sidebar-link dropdown-toggle-custom">
                        <Icon Name="IconName.MoneyBill" Class="sidebar-icon" />
                        <span>مالی</span>
                        <Icon Name="IconName.ChevronDown" Class="dropdown-arrow" />
                    </DropdownToggle>
                    <DropdownMenu Class="sidebar-dropdown">
                        <DropdownItem>
                            <NavLink class="dropdown-link" href="/payment">
                                <Icon Name="IconName.CreditCard" Class="sidebar-icon" />
                                <span>فیس</span>
                            </NavLink>
                        </DropdownItem>
                        <DropdownItem>
                            <NavLink class="dropdown-link" href="/reports">
                                <Icon Name="IconName.FileText" Class="sidebar-icon" />
                                <span>گزارشات</span>
                            </NavLink>
                        </DropdownItem>
                    </DropdownMenu>
                </Dropdown>
            </li>

            <!-- Timetable -->
            <li class="nav-item">
                <NavLink class="nav-link sidebar-link" href="/timetable">
                    <Icon Name="IconName.Clock" Class="sidebar-icon" />
                    <span>تقسیم اوقات</span>
                </NavLink>
            </li>

            <!-- Staff -->
            <li class="nav-item">
                <NavLink class="nav-link sidebar-link" href="/staff">
                    <Icon Name="IconName.UserTie" Class="sidebar-icon" />
                    <span>مدیریت کارمندان</span>
                </NavLink>
            </li>

            <!-- Settings -->
            <li class="nav-item">
                <NavLink class="nav-link sidebar-link" href="/settings">
                    <Icon Name="IconName.Cog" Class="sidebar-icon" />
                    <span>تنظیمات</span>
                </NavLink>
            </li>

        </ul>
    </nav>
</div>

@code {
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}

<style>
    .sidebar-nav {
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        width: 280px;
        background-color: #343a40;
        color: white;
        z-index: 1000;
        direction: rtl;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
    }

    .sidebar-brand {
        padding: 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        background-color: #212529;
    }

    .brand-link {
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
        font-weight: bold;
    }

        .brand-link:hover {
            color: #f8f9fa;
            text-decoration: none;
        }

    .brand-text {
        margin-right: 0.5rem;
    }

    .sidebar-menu {
        flex: 1;
        padding: 1rem 0;
        overflow-y: auto;
    }

    .sidebar-link {
        padding: 0.75rem 1.5rem !important;
        color: rgba(255,255,255,0.8) !important;
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        border: none;
        background: none;
        width: 100%;
        text-align: right;
        justify-content: flex-start;
    }

        .sidebar-link:hover {
            background-color: rgba(255,255,255,0.1) !important;
            color: white !important;
            transform: translateX(-5px);
            text-decoration: none;
        }

        .sidebar-link.active {
            background-color: #0d6efd !important;
            color: white !important;
            border-right: 4px solid #fff;
        }

    .sidebar-icon {
        margin-left: 0.75rem;
        width: 20px;
        text-align: center;
    }

    .dropdown-toggle-custom {
        justify-content: space-between;
    }

    .dropdown-arrow {
        margin-left: auto;
        margin-right: 0;
    }

    .sidebar-dropdown {
        background-color: rgba(0,0,0,0.2);
        border: none;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        margin: 0;
        padding: 0.5rem 0;
    }

    .dropdown-link {
        padding: 0.5rem 1.5rem 0.5rem 3rem;
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        display: flex;
        align-items: center;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

        .dropdown-link:hover {
            background-color: rgba(255,255,255,0.05);
            color: white;
            transform: translateX(-3px);
            text-decoration: none;
        }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .sidebar-nav

    {
        width: 100%;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar-nav.show {
        transform: translateX(0);
    }

    }
</style>