﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<Bar Breakpoint="Breakpoint.Desktop" 
     Background="Background.Dark" 
     ThemeContrast="ThemeContrast.Dark"
     Class="sidebar-nav">
    
    <BarBrand Class="sidebar-brand">
        <BarItem>
            <BarLink To="/">
                <Icon Name="IconName.School" Class="me-2" />
                <Text TextColor="TextColor.White" TextWeight="TextWeight.Bold">
                    مرکز زبان و مهارت‌های غالب
                </Text>
            </BarLink>
        </BarItem>
    </BarBrand>

    <BarToggler Bar="sidebar" />

    <BarMenu @ref="sidebar" Class="sidebar-menu">
        <BarStart>
            
            <!-- Dashboard -->
            <BarItem>
                <BarLink To="/" Match="NavLinkMatch.All">
                    <BarIcon IconName="IconName.Dashboard" />
                    <Text>داشبورد</Text>
                </BarLink>
            </BarItem>

            <!-- Enrollment Section -->
            <BarItem>
                <BarDropdown>
                    <BarDropdownToggle>
                        <BarIcon IconName="IconName.UserPlus" />
                        <Text>پذیرش</Text>
                        <BarDropdownIcon />
                    </BarDropdownToggle>
                    <BarDropdownMenu>
                        <BarDropdownItem>
                            <BarLink To="/enroll">
                                <BarIcon IconName="IconName.CreditCard" />
                                <Text>ثبت نام</Text>
                            </BarLink>
                        </BarDropdownItem>
                        <BarDropdownItem>
                            <BarLink To="/student">
                                <BarIcon IconName="IconName.Users" />
                                <Text>شاگردان</Text>
                            </BarLink>
                        </BarDropdownItem>
                        <BarDropdownItem>
                            <BarLink To="/home">
                                <BarIcon IconName="IconName.Home" />
                                <Text>تمام ثبت نام ها</Text>
                            </BarLink>
                        </BarDropdownItem>
                    </BarDropdownMenu>
                </BarDropdown>
            </BarItem>

            <!-- Finance Section -->
            <BarItem>
                <BarDropdown>
                    <BarDropdownToggle>
                        <BarIcon IconName="IconName.MoneyBill" />
                        <Text>مالی</Text>
                        <BarDropdownIcon />
                    </BarDropdownToggle>
                    <BarDropdownMenu>
                        <BarDropdownItem>
                            <BarLink To="/payment">
                                <BarIcon IconName="IconName.CreditCard" />
                                <Text>فیس</Text>
                            </BarLink>
                        </BarDropdownItem>
                        <BarDropdownItem>
                            <BarLink To="/reports">
                                <BarIcon IconName="IconName.FileText" />
                                <Text>گزارشات</Text>
                            </BarLink>
                        </BarDropdownItem>
                    </BarDropdownMenu>
                </BarDropdown>
            </BarItem>

            <!-- Timetable -->
            <BarItem>
                <BarLink To="/timetable">
                    <BarIcon IconName="IconName.Clock" />
                    <Text>تقسیم اوقات</Text>
                </BarLink>
            </BarItem>

            <!-- Staff -->
            <BarItem>
                <BarLink To="/staff">
                    <BarIcon IconName="IconName.UserTie" />
                    <Text>مدیریت کارمندان</Text>
                </BarLink>
            </BarItem>

            <!-- Settings -->
            <BarItem>
                <BarLink To="/settings">
                    <BarIcon IconName="IconName.Cog" />
                    <Text>تنظیمات</Text>
                </BarLink>
            </BarItem>

        </BarStart>
    </BarMenu>
</Bar>

@code {
    private Bar? sidebar;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}

<style>
    .sidebar-nav {
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        width: 280px;
        z-index: 1000;
        direction: rtl;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .sidebar-brand {
        padding: 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .sidebar-menu {
        padding: 1rem 0;
        height: calc(100vh - 80px);
        overflow-y: auto;
    }

    .sidebar-menu .bar-item {
        margin: 0.25rem 0;
    }

    .sidebar-menu .bar-link {
        padding: 0.75rem 1.5rem;
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        border-radius: 0;
    }

    .sidebar-menu .bar-link:hover {
        background-color: rgba(255,255,255,0.1);
        color: white;
        transform: translateX(-5px);
    }

    .sidebar-menu .bar-link.active {
        background-color: #0d6efd;
        color: white;
        border-right: 4px solid #fff;
    }

    .sidebar-menu .bar-icon {
        margin-left: 0.75rem;
        width: 20px;
        text-align: center;
    }

    .sidebar-menu .bar-dropdown-toggle {
        padding: 0.75rem 1.5rem;
        color: rgba(255,255,255,0.8);
        background: none;
        border: none;
        width: 100%;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;
    }

    .sidebar-menu .bar-dropdown-toggle:hover {
        background-color: rgba(255,255,255,0.1);
        color: white;
    }

    .sidebar-menu .bar-dropdown-menu {
        background-color: rgba(0,0,0,0.2);
        border: none;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        margin: 0;
        padding: 0.5rem 0;
    }

    .sidebar-menu .bar-dropdown-item .bar-link {
        padding: 0.5rem 1.5rem 0.5rem 3rem;
        font-size: 0.9rem;
    }

    .sidebar-menu .bar-dropdown-item .bar-link:hover {
        background-color: rgba(255,255,255,0.05);
        transform: translateX(-3px);
    }

    /* RTL Support */
    .sidebar-nav .bar-brand .bar-link {
        flex-direction: row-reverse;
    }

    .sidebar-nav .bar-dropdown-toggle {
        flex-direction: row-reverse;
    }

    .sidebar-nav .bar-dropdown-toggle .bar-dropdown-icon {
        margin-right: auto;
        margin-left: 0;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .sidebar-nav {
            width: 100%;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .sidebar-nav.show {
            transform: translateX(0);
        }
    }

    /* Scrollbar Styling */
    .sidebar-menu::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar-menu::-webkit-scrollbar-track {
        background: rgba(255,255,255,0.1);
    }

    .sidebar-menu::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.3);
        border-radius: 3px;
    }

    .sidebar-menu::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.5);
    }
</style>