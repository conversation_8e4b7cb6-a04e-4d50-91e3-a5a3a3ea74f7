﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<div class="sidebar-nav">
    <!-- Brand -->
    <div class="sidebar-brand">
        <a href="/" class="brand-link">
            <i class="bi bi-mortarboard me-2"></i>
            <span class="brand-text">مرکز زبان و مهارت‌های غالب</span>
        </a>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-menu">
        <ul class="nav flex-column">

            <!-- Dashboard -->
            <li class="nav-item">
                <NavLink class="nav-link sidebar-link" href="/" Match="NavLinkMatch.All">
                    <i class="bi bi-speedometer2 sidebar-icon"></i>
                    <span>داشبورد</span>
                </NavLink>
            </li>

            <!-- Enrollment Section -->
            <li class="nav-item">
                <div class="dropdown">
                    <button class="nav-link sidebar-link dropdown-toggle-custom" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-plus sidebar-icon"></i>
                        <span>پذیرش</span>
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                    </button>
                    <ul class="dropdown-menu sidebar-dropdown">
                        <li>
                            <NavLink class="dropdown-item dropdown-link" href="/enroll">
                                <i class="bi bi-credit-card sidebar-icon"></i>
                                <span>ثبت نام</span>
                            </NavLink>
                        </li>
                        <li>
                            <NavLink class="dropdown-item dropdown-link" href="/student">
                                <i class="bi bi-people sidebar-icon"></i>
                                <span>شاگردان</span>
                            </NavLink>
                        </li>
                        <li>
                            <NavLink class="dropdown-item dropdown-link" href="/home">
                                <i class="bi bi-house sidebar-icon"></i>
                                <span>تمام ثبت نام ها</span>
                            </NavLink>
                        </li>
                    </ul>
                </div>
            </li>

            <!-- Finance Section -->
            <li class="nav-item">
                <div class="dropdown">
                    <button class="nav-link sidebar-link dropdown-toggle-custom" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-currency-dollar sidebar-icon"></i>
                        <span>مالی</span>
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                    </button>
                    <ul class="dropdown-menu sidebar-dropdown">
                        <li>
                            <NavLink class="dropdown-item dropdown-link" href="/payment">
                                <i class="bi bi-credit-card sidebar-icon"></i>
                                <span>فیس</span>
                            </NavLink>
                        </li>
                        <li>
                            <NavLink class="dropdown-item dropdown-link" href="/reports">
                                <i class="bi bi-file-text sidebar-icon"></i>
                                <span>گزارشات</span>
                            </NavLink>
                        </li>
                    </ul>
                </div>
            </li>

            <!-- Timetable -->
            <li class="nav-item">
                <NavLink class="nav-link sidebar-link" href="/timetable">
                    <i class="bi bi-clock sidebar-icon"></i>
                    <span>تقسیم اوقات</span>
                </NavLink>
            </li>

            <!-- Staff -->
            <li class="nav-item">
                <NavLink class="nav-link sidebar-link" href="/staff">
                    <i class="bi bi-person-badge sidebar-icon"></i>
                    <span>مدیریت کارمندان</span>
                </NavLink>
            </li>

            <!-- Settings -->
            <li class="nav-item">
                <NavLink class="nav-link sidebar-link" href="/settings">
                    <i class="bi bi-gear sidebar-icon"></i>
                    <span>تنظیمات</span>
                </NavLink>
            </li>

        </ul>
    </nav>
</div>

@code {
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}

<style>
    .sidebar-nav {
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        width: 280px;
        background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
        color: white;
        z-index: 1000;
        direction: rtl;
        box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
    }

    .sidebar-brand {
        padding: 1.5rem 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        background: rgba(0,0,0,0.1);
    }

    .brand-link {
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
        font-weight: bold;
        transition: color 0.3s ease;
    }

        .brand-link:hover {
            color: #3498db;
            text-decoration: none;
        }

    .brand-text {
        margin-right: 0.5rem;
    }

    .sidebar-menu {
        flex: 1;
        padding: 1rem 0;
        overflow-y: auto;
    }

    .nav-item {
        margin: 0.25rem 0;
    }

    .sidebar-link {
        padding: 0.75rem 1.5rem !important;
        color: rgba(255,255,255,0.9) !important;
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        border: none;
        background: none;
        width: 100%;
        text-align: right;
        justify-content: flex-start;
        border-radius: 0;
    }

        .sidebar-link:hover {
            background: rgba(52, 152, 219, 0.2) !important;
            color: white !important;
            transform: translateX(-5px);
            text-decoration: none;
            border-right: 3px solid #3498db;
        }

        .sidebar-link.active {
            background: linear-gradient(90deg, #3498db, #2980b9) !important;
            color: white !important;
            border-right: 4px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

    .sidebar-icon {
        margin-left: 0.75rem;
        width: 20px;
        text-align: center;
        font-size: 1.1rem;
    }

    .dropdown-toggle-custom {
        justify-content: space-between;
        cursor: pointer;
    }

    .dropdown-arrow {
        margin-left: auto;
        margin-right: 0;
        transition: transform 0.3s ease;
    }

    .dropdown:hover .dropdown-arrow {
        transform: rotate(180deg);
    }

    .sidebar-dropdown {
        background: rgba(0,0,0,0.3);
        border: none;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
        margin: 0;
        padding: 0.5rem 0;
        position: static;
        transform: none;
        display: block;
        width: 100%;
        border-radius: 0;
    }

    .dropdown-link {
        padding: 0.5rem 1.5rem 0.5rem 3rem !important;
        color: rgba(255,255,255,0.8) !important;
        text-decoration: none;
        display: flex;
        align-items: center;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
    }

        .dropdown-link:hover {
            background: rgba(52, 152, 219, 0.15) !important;
            color: white !important;
            transform: translateX(-3px);
            text-decoration: none;
            border-right: 2px solid #3498db;
        }

        .dropdown-link.active {
            background: rgba(52, 152, 219, 0.3) !important;
            color: white !important;
            border-right: 3px solid #3498db;
        }

    /* Custom dropdown behavior */
    .dropdown:hover .dropdown-menu {
        display: block;
    }

    .dropdown-menu {
        display: none;
    }

    /* Mobile Responsiveness */
   
  
</style>